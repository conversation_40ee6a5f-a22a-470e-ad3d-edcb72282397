{"name": "dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.7.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "js-cookie": "^3.0.5", "lucide-react": "^0.503.0", "next": "15.3.1", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.5", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "tailwind-merge": "^2.0.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8.4.31", "prettier": "^3.5.3", "tailwindcss": "^3.3.0"}}